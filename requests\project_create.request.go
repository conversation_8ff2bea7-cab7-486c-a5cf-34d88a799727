package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectCreate struct {
	core.BaseValidator
	Name           *string `json:"name"`
	Code           *string `json:"code"`
	Description    *string `json:"description"`
	StartWorkingAt *string `json:"working_start_at"`
	EndWorkingAt   *string `json:"working_end_at"`
}

func (r *ProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Code, "code"))
	// code must uppeprcase and no empty

	r.Must(r.IsStrUnique(ctx, r.Name, models.Project{}.TableName(), "name", "", "name"))
	r.Must(r.<PERSON>tr<PERSON>(ctx, r.Code, models.Project{}.TableName(), "code", "", "code"))

	return r.<PERSON><PERSON>()
}
