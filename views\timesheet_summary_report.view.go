package views

import "gitlab.finema.co/finema/finework/finework-api/models"

type TimesheetSummaryReportProjectTiming struct {
	ProjectCode *string `json:"project_code"`
	ProjectName *string `json:"project_name"`
	Type        string  `json:"type"`
	TotalTiming float64 `json:"total_timing"`
}

type TimesheetSummaryReportView struct {
	*models.User
	TotalTiming         float64                               `json:"total_timing"`
	TotalProjectTiming  float64                               `json:"total_project_timing"`
	TotalLeaveTiming    float64                               `json:"total_leave_timing"`
	TotalSgaTiming      float64                               `json:"total_sga_timing"`
	TotalInternalTiming float64                               `json:"total_internal_timing"`
	TotalExternalTiming float64                               `json:"total_external_timing"`
	TotalOtTiming       float64                               `json:"total_ot_timing"`
	Timings             []TimesheetSummaryReportProjectTiming `json:"timings"`
}
